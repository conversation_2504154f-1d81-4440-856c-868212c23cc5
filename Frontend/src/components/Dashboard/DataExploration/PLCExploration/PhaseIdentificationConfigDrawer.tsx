import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Form,
  Button,
  Typography,
  message,
  Spin,
  Select
} from 'antd';
import { CloseOutlined, BranchesOutlined, CalendarOutlined } from '@ant-design/icons';
import {
  PLCPanelConfiguration
} from './types/PLCTypes';
import { postRequest } from '../../../../utils/apiHandler';

const { Title } = Typography;
const { Option } = Select;

interface PhaseIdentificationConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  panelId: string | null;
  configuration?: PLCPanelConfiguration;
  onConfigurationSave: (panelId: string, config: PLCPanelConfiguration) => void;
}

const PhaseIdentificationConfigDrawer: React.FC<PhaseIdentificationConfigDrawerProps> = ({
  open,
  onClose,
  panelId,
  configuration,
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [selectedBatches, setSelectedBatches] = useState<string[]>([]);
  const [selectedPhases, setSelectedPhases] = useState<string[]>([]);
  const [basicSettingsLoading, setBasicSettingsLoading] = useState(false);

  // Get data from configuration (similar to batch comparison)
  const isDataLoaded = configuration?.apiData?.selectedBatches &&
                       configuration.apiData.selectedBatches.length > 0;
  const availableBatches = configuration?.apiData?.selectedBatches || [];
  const dateRange = configuration?.dataRange;
  const availableColumns = configuration?.apiData?.columns || [];
  const allColumns = ['DateTime', ...availableColumns];

  // Get available phases from metadata
  const availablePhases = (configuration?.apiData?.metadata as any)?.phases || [];

  // Master loading state
  const isMasterLoading = basicSettingsLoading;

  // Get current loading message based on active operation
  const getLoadingMessage = () => {
    if (basicSettingsLoading) return 'Loading phase comparison...';
    return 'Processing...';
  };

  // Handle drawer close
  const handleClose = () => {
    form.resetFields();
    setSelectedBatches([]);
    onClose();
  };

  // Initialize form with configuration data when drawer opens
  useEffect(() => {
    if (open && configuration) {
      // Set default batch selection (ALL available batches for phase comparison)
      if (availableBatches.length > 0) {
        const defaultBatches = configuration.apiData?.selectedBatchIds || availableBatches; // Pre-select ALL batches
        setSelectedBatches(defaultBatches);

        // Get Y-axis columns from API response metadata (most reliable) or fallback to configuration
        const apiSelectedColumns = (configuration.apiData?.metadata as any)?.y_axis || [];
        const configSelectedColumns = configuration.basic?.selectedColumns?.headers || [];
        const defaultYAxisColumns = apiSelectedColumns.length > 0 ? apiSelectedColumns : configSelectedColumns;

        // Preserve user's phase selection if it exists, otherwise default to all available phases
        const existingPhaseSelection = configuration.apiData?.selectedPhases;
        const defaultPhases = existingPhaseSelection && existingPhaseSelection.length > 0
          ? existingPhaseSelection
          : (availablePhases.length > 0 ? availablePhases : []);
        setSelectedPhases(defaultPhases);

        form.setFieldsValue({
          selectedBatches: defaultBatches,
          // xAxisColumn: configuration.basic?.xAxisColumn || 'step_id', // Removed X-axis selection
          yAxis: defaultYAxisColumns, // Use API response metadata as source of truth
          group: 'batch_id', // Always default to batch_id for phase comparison
          system: configuration.apiData?.system || 'VAT',
          phaseIdentifier: 'Phase',
          batchIdentifier: 'batch_id',
          selectedPhases: defaultPhases
        });
      }
    }
  }, [open, configuration, form, availableBatches, availablePhases]);

  // Handle applying basic settings changes
  const handleApplyBasicSettings = async () => {
    if (!isDataLoaded || !panelId) {
      message.error('Please load data first');
      return;
    }

    try {
      await form.validateFields(['selectedBatches', 'yAxis', 'group']);
    } catch (error) {
      return;
    }

    const formValues = form.getFieldsValue();
    const { selectedBatches, yAxis, group, system, phaseIdentifier, batchIdentifier, selectedPhases } = formValues;
    const xAxisColumn = 'step_id'; // Fixed X-axis to step_id

    if (!selectedBatches || selectedBatches.length === 0) {
      message.error('Please select at least one batch for phase comparison');
      return;
    }

    // X-axis is now fixed to step_id, no validation needed

    if (!yAxis || yAxis.length === 0) {
      message.error('Please select at least one Y-axis column');
      return;
    }

    if (!group) {
      message.error('Please select identifier');
      return;
    }

    setBasicSettingsLoading(true);
    try {
      //payload for phase comparison
      const requestPayload = {
        start_datetime: `${dateRange?.startDate} ${dateRange?.startTime || '00:00'}:00`,
        end_datetime: `${dateRange?.endDate} ${dateRange?.endTime || '23:59'}:59`,
        batch_ids: selectedBatches, // Changed to array format
        system: system || 'VAT',
        x_axis: 'step_id', // Fixed X-axis to step_id
        y_axis: yAxis || [],
        phase_identifier: phaseIdentifier || 'Phase',
        batch_identifier: batchIdentifier || 'batch_id',
        phases: selectedPhases || [] // Include selected phases in API payload
      };

      const response = await postRequest('/file/explore-plc/phase-identification', requestPayload);

      if (response.data.data) {
        const apiData = response.data.data;

        // Create configuration with loaded data
        const phaseConfiguration: PLCPanelConfiguration = {
          ...configuration,
          basic: {
            xAxisColumn,
            selectedColumns: {
              indices: yAxis.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
              headers: yAxis
            },
            group
          },
          advanced: {
            windowMode: false,
          },
          title: `Phase Comparison - ${yAxis.join(', ')} (Batches: ${selectedBatches.join(', ')})${selectedPhases && selectedPhases.length > 0 ? ` - Phases: ${selectedPhases.join(', ')}` : ''}`,
          panelType: 'PhaseIdentificationPanel' as any,
          lastModified: new Date().toISOString(),
          apiData: {
            ...configuration?.apiData,
            ...apiData,
            selectedBatchIds: selectedBatches, // Store selected batch IDs array
            batchId: selectedBatches[0], // Keep first batch for backward compatibility
            system: system || 'VAT',
            xAxis: 'step_id', // Fixed X-axis to step_id
            yAxis: apiData.metadata?.y_axis || yAxis[0],
            identifier: group || 'batch_id',
            phase_identifier: phaseIdentifier || 'Phase',
            selectedPhases: selectedPhases || [] // Store selected phases
          }
        };

        onConfigurationSave(panelId, phaseConfiguration);
        message.success('Phase comparison data loaded successfully');
        handleClose();
      } else {
        message.error('No data received from phase comparison API');
      }
    } catch (error: any) {
      console.error('Phase Comparison API Error:', error);
      message.error(error.message || 'Failed to load phase comparison data');
    } finally {
      setBasicSettingsLoading(false);
    }
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <BranchesOutlined style={{ color: '#52c41a' }} />
            Phase Comparison Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
            disabled={isMasterLoading}
          />
        </div>
      }
      placement="right"
      width={450}
      open={open}
      onClose={handleClose}
      closable={false}
      maskClosable={!isMasterLoading}
      styles={{
        body: { padding: '0', position: 'relative' }
      }}
    >
      {/* Full Screen Loading Overlay */}
      <Spin 
        spinning={isMasterLoading} 
        tip={getLoadingMessage()}
        size="large"
        style={{
          minHeight: '100vh'
        }}
      >
        <div 
          className="phase-identification-config-drawer" 
          style={{ 
            minHeight: '100vh',
            pointerEvents: isMasterLoading ? 'none' : 'auto'
          }}
          onKeyDown={(e) => {
            if (isMasterLoading) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
          <Form
            form={form}
            layout="vertical"
            className="phase-identification-config-form"
          >
            {/* Date Range Section - Disabled */}
            <div style={{
              background: '#f5f5f5',
              borderRadius: '12px',
              padding: '20px',
              margin: '16px',
              marginBottom: '20px',
              border: '1px solid #d9d9d9',
              opacity: 0.7
            }}>
              <div style={{
                marginBottom: '16px',
                paddingBottom: '12px',
                borderBottom: '2px solid #999'
              }}>
                <Title level={5} style={{
                  margin: 0,
                  color: '#666',
                  fontSize: '16px',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <CalendarOutlined /> Date Range (From Batch Comparison)
                </Title>
              </div>

              <div style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
                <div style={{ flex: 1 }}>
                  <label style={{ fontSize: '12px', color: '#666', marginBottom: '4px', display: 'block' }}>
                    Start Date & Time
                  </label>
                  <div style={{
                    padding: '8px 12px',
                    background: '#fafafa',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    fontSize: '13px',
                    color: '#666'
                  }}>
                    {dateRange?.startDate} {dateRange?.startTime || '00:00'}
                  </div>
                </div>
                <div style={{ flex: 1 }}>
                  <label style={{ fontSize: '12px', color: '#666', marginBottom: '4px', display: 'block' }}>
                    End Date & Time
                  </label>
                  <div style={{
                    padding: '8px 12px',
                    background: '#fafafa',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    fontSize: '13px',
                    color: '#666'
                  }}>
                    {dateRange?.endDate} {dateRange?.endTime || '23:59'}
                  </div>
                </div>
              </div>
            </div>



            {/* Basic Settings */}
            <div style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '20px',
              margin: '0 16px 20px 16px',
              border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
              boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
              opacity: isDataLoaded ? 1 : 0.5
            }}>
              <div style={{
                marginBottom: '20px',
                paddingBottom: '12px',
                borderBottom: `2px solid ${isDataLoaded ? '#52c41a' : '#d9d9d9'}`
              }}>
                <Title level={5} style={{
                  margin: 0,
                  color: isDataLoaded ? '#52c41a' : '#999',
                  fontSize: '16px',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  ⚙️ Basic Settings
                  {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
                </Title>
              </div>
              <div>
                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Select Batches for Phase Comparison</span>}
                  name="selectedBatches"
                  style={{ marginBottom: '16px' }}
                  rules={[{ required: true, message: 'Please select at least one batch!' }]}
                >
                  <Select
                    mode="multiple" // Enable multi-select
                    placeholder="Select batches"
                    value={selectedBatches}
                    onChange={setSelectedBatches}
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{ borderRadius: '8px' }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.value as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {availableBatches.map(batch => (
                      <Option key={batch} value={batch}>
                        {batch}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* X-axis is now fixed to 'step_id' - removed user selection */}
                {/*
                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>X Axis</span>}
                  name="xAxisColumn"
                  style={{ marginBottom: '16px' }}
                >
                  <Select
                    placeholder="Select X Axis Column"
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {allColumns.map(column => (
                      <Option key={column} value={column}>
                        {column}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                */}

                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Y Axis (Multi-select)</span>}
                  name="yAxis"
                  style={{ marginBottom: '16px' }}
                >
                  <Select
                    mode="multiple"
                    placeholder="Select columns for Y Axis"
                    allowClear
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    maxTagCount="responsive"
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {availableColumns.map(column => (
                      <Option key={column} value={column}>
                        {column}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Identifier</span>}
                  name="group"
                  style={{ marginBottom: '16px' }}
                  initialValue="batch_id"
                >
                  <Select
                    placeholder="batch_id"
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                    defaultValue="batch_id"
                  >
                    <Option key="batch_id" value="batch_id">
                      batch_id
                    </Option>
                  </Select>
                </Form.Item>

                {/* Phase Selection */}
                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Select Phases (Optional)</span>}
                  name="selectedPhases"
                  style={{ marginBottom: '16px' }}
                  help={availablePhases.length === 0 ? "No phases available." : `${availablePhases.length} phases available`}
                >
                  <Select
                    mode="multiple"
                    placeholder={availablePhases.length > 0 ? "Select phases to analyze" : "No phases available"}
                    value={selectedPhases}
                    onChange={setSelectedPhases}
                    disabled={!isDataLoaded || basicSettingsLoading || availablePhases.length === 0}
                    size="middle"
                    style={{ borderRadius: '8px' }}
                    showSearch
                    allowClear
                    maxTagCount="responsive"
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {availablePhases.map((phase: string) => (
                      <Option key={phase} value={phase}>
                        {phase}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* Apply Basic Settings Button */}
                <div style={{ marginTop: '20px' }}>
                  <Button
                    type="primary"
                    loading={basicSettingsLoading}
                    disabled={!isDataLoaded || basicSettingsLoading}
                    onClick={handleApplyBasicSettings}
                    style={{
                      width: '100%',
                      height: '40px',
                      fontSize: '14px',
                      fontWeight: 600,
                      borderRadius: '8px',
                      background: '#52c41a',
                      borderColor: '#52c41a'
                    }}
                    size="large"
                  >
                    {basicSettingsLoading ? 'Loading Phase Comparison...' : 'Load Phase Comparison'}
                  </Button>
                </div>
              </div>
            </div>
          </Form>
        </div>
      </Spin>
    </Drawer>
  );
};

export default PhaseIdentificationConfigDrawer; 